# 需求 [compress_pic]

## 反馈

1. Rain反馈之前下载的图片部分没压缩，需要batch直接判断大小并压缩，不用改文件名
2. Maggie反馈可以直接写shell 操作。处理中失败直接退出
3. Fred反馈使用batch来做,已经写好的shell脚本记录在需求文档,对比batch进行效率测试

## 需求提出人:   Rain

## 修改人：      Lu<PERSON> xiaowei

## 提出日期:     2025-08-19

## 原因
1. 以前下载的图片没有压缩,会导致前端展示图片速度慢。

## 解决办法

1. 添加batch,给定目录与目标size,遍历目录下的所有图片,如果大于目标size,就压缩到目标size。(使用gofile)

## 是否需要补充UT

1. 不需要

## 确认日期:    2025-08-21

## 压缩的shell脚本

1. 需要安装ImageMagick
2. 执行脚本, eg: ./compress_images.sh -d [dir] -s [dest_size]
3. 脚本内容:
```shell
#!/bin/bash

# 默认值
DIR=""
SIZE_LIMIT=200 # KB
READ_COUNT=0
COMPRESS_COUNT=0
FAIL_LIST=()
RECURSIVE=true
MAX_SIZE=1280   # 最大宽度/高度

usage() {
  echo "Usage: $0 -d <directory> [-s <size_in_kb>] [-m <max_width_height>] [-n]"
  echo "  -d    图片目录"
  echo "  -s    图片大小限制，单位KB (默认: 200)"
  echo "  -m    图片最大宽度/高度 (默认: 1280)"
  echo "  -n    不递归子目录 (默认递归)"
  exit 1
}

# 参数解析
while getopts "d:s:m:n" opt; do
  case $opt in
    d) DIR="$OPTARG" ;;
    s) SIZE_LIMIT="$OPTARG" ;;
    m) MAX_SIZE="$OPTARG" ;;
    n) RECURSIVE=false ;;
    *) usage ;;
  esac
done

if [[ -z "$DIR" ]]; then
  usage
fi

if [[ ! -d "$DIR" ]]; then
  echo "错误：路径 $DIR 不存在或不是目录"
  exit 1
fi

echo "扫描目录: $DIR"
echo "大小限制: ${SIZE_LIMIT}KB"
echo "最大宽度/高度: ${MAX_SIZE}"
echo "是否递归: $RECURSIVE"

# 查找命令，根据是否递归选择
if $RECURSIVE; then
  IMAGES=$(find "$DIR" -type f \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" \))
else
  IMAGES=$(find "$DIR" -maxdepth 1 -type f \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" \))
fi

for img in $IMAGES; do
  ((READ_COUNT++))
  size_kb=$(du -k "$img" | cut -f1)
  
  if (( size_kb > SIZE_LIMIT )); then
    echo "压缩: $img (${size_kb}KB)"
    # 按比例缩放，限制最大宽高
    mogrify -resize "${MAX_SIZE}x${MAX_SIZE}>" "$img"
    if [[ $? -eq 0 ]]; then
      ((COMPRESS_COUNT++))
    else
      FAIL_LIST+=("$img")
    fi
  fi
done

echo "====== 统计信息 ======"
echo "读取图片总数: $READ_COUNT"
echo "压缩图片数量: $COMPRESS_COUNT"
if [[ ${#FAIL_LIST[@]} -gt 0 ]]; then
  echo "压缩失败的图片:"
  for f in "${FAIL_LIST[@]}"; do
    echo "  $f"
  done
else
  echo "所有图片压缩成功"
fi
```

## online-step

1. 运行batch
```
./start.sh  -n fixPhoP -d "goresodownload" -cmd "cmd/batch/fixPhoP/main.go -dryrun -board=TRB"
```
